import { AxiosError } from "axios";
import { type OnboardingRequest } from "../../functions/merchants/schemas/onboarding.schema.js";
import { logger } from "../../helpers/logger.js";
import { PayrixMerchantResponse } from "../../types/payrix.types.js";
import { createPayrixApiClient, PAYRIX_API_URL } from "./api-client.js";

const apiClient = createPayrixApiClient();

export async function createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse> {
  try {
    logger.info("Sending request to Payrix API", {
      url: `${PAYRIX_API_URL}/entities`,
      headers: { "Content-Type": "application/json", APIKEY: "[REDACTED]" },
      merchantEmail: merchantData.email,
      merchantName: merchantData.name,
      hasBankVerification: !!merchantData.bankVerification,
      bankVerificationMethod: merchantData.bankVerification?.verificationMethod,
    });

    logger.info("Merchant data check", {
      merchantData,
    });

    const response = await apiClient.post("/entities", merchantData);

    logger.info("Payrix API response", {
      status: response.status,
      data: response.data,
    });

    const entityData = response.data?.response?.data?.[0];
    if (!entityData) {
      throw new Error("Invalid Payrix response structure: no entity data found");
    }

    return entityData;
  } catch (error) {
    const axiosError = error as AxiosError;
    logger.error("Payrix API Error", {
      status: axiosError.response?.status,
      statusText: axiosError.response?.statusText,
      data: axiosError.response?.data,
      message: axiosError.message,
    });

    throw new Error(
      `Payrix API Error (${axiosError.response?.status}): ${JSON.stringify(
        axiosError.response?.data || axiosError.message
      )}`
    );
  }
}

export async function createPlaidLinkToken(linkTokenData: {
  userId: string;
  countryCode: string;
  redirectUri: string;
}): Promise<{ linkToken: string; requestId: string }> {
  try {
    logger.info("Creating Plaid link token via Payrix", {
      userId: linkTokenData.userId,
      countryCode: linkTokenData.countryCode,
      redirectUri: linkTokenData.redirectUri,
    });

    const response = await apiClient.post("/plaid/linkToken/create", linkTokenData);

    logger.info("Plaid link token creation response", {
      status: response.status,
      hasLinkToken: !!response.data?.Response?.responses?.[0]?.linkToken,
      requestId: response.data?.Response?.responses?.[0]?.requestId,
    });

    const linkTokenResponse = response.data?.Response?.responses?.[0];
    if (!linkTokenResponse?.linkToken) {
      throw new Error("Invalid Payrix response structure: no link token found");
    }

    return {
      linkToken: linkTokenResponse.linkToken,
      requestId: linkTokenResponse.requestId,
    };
  } catch (error) {
    handlePlaidLinkTokenError(error as AxiosError, linkTokenData);
    throw error;
  }
}

function handlePlaidLinkTokenError(error: AxiosError, linkTokenData: {
  userId: string;
  countryCode: string;
  redirectUri: string;
}): void {
  logger.error("Error creating Plaid link token", {
    error,
    userId: linkTokenData.userId,
    countryCode: linkTokenData.countryCode,
  });

  if (error instanceof AxiosError) {
    logger.error("Payrix API error details", {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });

    const status = error.response?.status;
    if (status === 400) {
      throw new Error("Invalid request parameters for Plaid link token creation");
    } else if (status === 401) {
      throw new Error("Authentication failed with Payrix API");
    } else if (status === 403) {
      throw new Error("Plaid integration not enabled for this account");
    }
  }
}